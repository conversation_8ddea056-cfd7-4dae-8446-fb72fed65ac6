@echo off
echo Starting NVH Backend Server...
echo.

REM Activate virtual environment
call .venv\Scripts\activate

REM Install dependencies if needed
echo Installing/updating dependencies...
pip install -r requirements.txt

REM Run migrations
echo Running database migrations...
python manage.py makemigrations
python manage.py migrate

REM Start Django server
echo Starting Django server on http://localhost:8000
echo Press Ctrl+C to stop the server
echo.
python manage.py runserver 0.0.0.0:8000
