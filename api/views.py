"""
API Views for NVH Backend
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.conf import settings

User = get_user_model()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_info(request):
    """
    Get current user information
    """
    user = request.user
    return Response({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'is_staff': user.is_staff,
        'is_superuser': user.is_superuser,
        'date_joined': user.date_joined,
        'last_login': user.last_login,
    })


@api_view(['GET'])
def health_check(request):
    """
    Health check endpoint
    """
    return Response({
        'status': 'healthy',
        'message': 'NVH Backend API is running'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_data(request):
    """
    Get dashboard data for the home page
    """
    return Response({
        'welcome_message': f'欢迎, {request.user.first_name or request.user.username}!',
        'system_info': {
            'name': 'NVH 数据管理系统',
            'version': '2.0.0',
            'description': '基于 Django + Vue 的 NVH 数据管理系统'
        },
        'user_stats': {
            'total_users': User.objects.count(),
            'current_user': request.user.username
        }
    })
