# NVH 数据管理系统

基于 Django + Vue 3 的前后端分离架构，集成 Keycloak 认证的 NVH 数据管理系统。

## 技术栈

### 后端
- Django 5.1
- Django REST Framework
- Python Keycloak
- MySQL
- CORS 支持

### 前端
- Vue 3
- Element Plus
- Axios
- Keycloak JS
- Vite

## 项目结构

```
nvh_django/
├── nvh_backend/          # Django 后端项目
│   ├── settings.py       # Django 配置
│   ├── urls.py          # 主路由配置
│   └── ...
├── api/                 # Django API 应用
│   ├── views.py         # API 视图
│   ├── urls.py          # API 路由
│   ├── authentication.py # Keycloak 认证
│   └── ...
├── frontend/            # Vue 前端项目
│   ├── src/
│   │   ├── views/       # 页面组件
│   │   ├── utils/       # 工具函数
│   │   ├── api/         # API 接口
│   │   └── router/      # 路由配置
│   ├── package.json
│   └── vite.config.js
├── manage.py           # Django 管理脚本
├── requirements.txt    # Python 依赖
├── .env               # 环境变量
└── README.md          # 项目说明
```

## 开发环境搭建

### 1. 后端环境

```bash
# 激活虚拟环境
.venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建超级用户（可选）
python manage.py createsuperuser

# 启动后端服务
python manage.py runserver 0.0.0.0:8000
```

### 2. 前端环境

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 配置说明

### 环境变量 (.env)

```env
# Django 设置
SECRET_KEY=nvh-system-secret-key-2024
DEBUG=True

# 数据库设置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=nvh_data

# Keycloak 设置
KEYCLOAK_SERVER_URL=https://account-test.sgmw.com.cn/auth/
KEYCLOAK_REALM=demo
KEYCLOAK_CLIENT_ID=backend
KEYCLOAK_CLIENT_SECRET=8545c061-7cf7-41e5-b92b-e6769a6a75b8
KEYCLOAK_FRONTEND_CLIENT_ID=front
```

### Keycloak 配置

- **服务器地址**: https://account-test.sgmw.com.cn/auth/
- **Realm**: demo
- **前端客户端ID**: front (public)
- **后端客户端ID**: backend (bearer-only)
- **测试账号**: test / B5FDs0PcyuTipj^！

## API 接口

### 认证相关
- `GET /api/health/` - 健康检查
- `GET /api/user/info/` - 获取用户信息
- `GET /api/dashboard/` - 获取仪表板数据

## 功能特性

### 已实现功能
- ✅ Keycloak 单点登录
- ✅ JWT Token 认证
- ✅ 用户信息管理
- ✅ 响应式首页界面
- ✅ 前后端分离架构

### 计划功能
- 🔄 数据上传管理
- 🔄 报告生成
- 🔄 权限管理
- 🔄 系统设置

## 开发指南

### 添加新的 API 接口

1. 在 `api/views.py` 中添加视图函数
2. 在 `api/urls.py` 中添加路由
3. 在前端 `src/api/index.js` 中添加接口调用

### 添加新的页面

1. 在 `frontend/src/views/` 中创建 Vue 组件
2. 在 `frontend/src/router/index.js` 中添加路由

## 部署说明

### 生产环境配置

1. 修改 `.env` 文件中的配置
2. 设置 `DEBUG=False`
3. 配置生产数据库
4. 构建前端项目：`npm run build`
5. 配置 Nginx 反向代理

## 故障排除

### 常见问题

1. **Keycloak 认证失败**
   - 检查 Keycloak 服务器是否可访问
   - 确认客户端配置正确

2. **数据库连接失败**
   - 检查 MySQL 服务是否启动
   - 确认数据库配置正确

3. **CORS 错误**
   - 检查 Django CORS 配置
   - 确认前端请求地址正确

## 联系方式

如有问题，请联系开发团队。
